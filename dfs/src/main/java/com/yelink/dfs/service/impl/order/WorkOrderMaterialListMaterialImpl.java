package com.yelink.dfs.service.impl.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yelink.dfs.constant.product.BomItemTypeEnum;
import com.yelink.dfs.constant.product.BomStateEnum;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ReplaceSchemeEntity;
import com.yelink.dfs.entity.product.ReplaceSchemeMaterialEntity;
import com.yelink.dfs.entity.product.dto.BomSelectDTO;
import com.yelink.dfs.entity.product.dto.MaterialsSelectDTO;
import com.yelink.dfs.entity.product.dto.ReplaceSchemeSelectDTO;
import com.yelink.dfs.mapper.order.WorkOrderMaterialListMaterialMapper;
import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfs.service.order.WorkOrderMaterialListMaterialService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ReplaceSchemeService;
import com.yelink.dfscommon.api.wms.StockInventoryDetailInterface;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.dfs.OrderNumTypeEnum;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierInfoDTO;
import com.yelink.dfscommon.dto.ams.open.order.ProductOrderMaterialListMaterialEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListMaterialEntity;
import com.yelink.dfscommon.entity.wms.StockMaterialDetailEntity;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/9/1 14:52
 */
@Service
public class WorkOrderMaterialListMaterialImpl extends ServiceImpl<WorkOrderMaterialListMaterialMapper, WorkOrderMaterialListMaterialEntity> implements WorkOrderMaterialListMaterialService {
    @Resource
    private MaterialService materialService;
    @Resource
    private StockInventoryDetailInterface stockInventoryDetailInterface;
    @Resource
    private ReplaceSchemeService replaceSchemeService;
    @Resource
    @Lazy
    private BomService bomService;
    @Resource
    private OrderPushDownIdentifierService orderPushDownIdentifierService;

    @Override
    public List<WorkOrderMaterialListMaterialEntity> listMaterialListMaterials(Integer materialListId) {
        List<WorkOrderMaterialListMaterialEntity> materialEntities = this.lambdaQuery()
                .in(WorkOrderMaterialListMaterialEntity::getMaterialListId, materialListId)
                .list();

        // 调整用料清单物料的位置，呈现标准件与替代件的子父级关系
        List<WorkOrderMaterialListMaterialEntity> list = sortMaterialEntities(materialEntities);
        // 查找用料清单关联的bom
        List<Integer> bomRawMaterialIds = list.stream()
                .map(WorkOrderMaterialListMaterialEntity::getBomRawMaterialId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        BomSelectDTO selectDTO = BomSelectDTO.builder()
                .state(String.valueOf(BomStateEnum.RELEASED.getCode()))
                .isTemplate(false)
                .bomRawMaterialIds(bomRawMaterialIds)
                .build();
        Map<Integer, BomRawMaterialEntity> bomRawMaterialMap = new HashMap<>();
        Map<Integer, String> replaceMaterialMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(bomRawMaterialIds)) {
            Page<BomEntity> page = bomService.list(selectDTO);
            List<BomRawMaterialEntity> bomRawMaterialEntities = page.getRecords().stream().flatMap(bomEntity -> bomEntity.getBomRawMaterialEntities().stream()).collect(Collectors.toList());
            bomRawMaterialMap = bomRawMaterialEntities.stream()
                    .collect(Collectors.toMap(BomRawMaterialEntity::getId, o -> o));
            replaceMaterialMap = bomRawMaterialEntities.stream().filter(o -> StringUtils.isNotBlank(o.getReplaceMaterialId()))
                    .collect(Collectors.toMap(BomRawMaterialEntity::getId, BomRawMaterialEntity::getReplaceMaterialId));
        }

        // 查询物料当前库存数量
        List<StockMaterialDetailEntity> materialDetails = new ArrayList<>();
        list.forEach(o -> materialDetails.add(StockMaterialDetailEntity.builder().productCode(o.getMaterialCode()).skuId(o.getSkuId()).businessUnitCode(o.getBusinessUnitCode()).build()));
        Map<String, BigDecimal> materialCodeStockMap = JacksonUtil.getResponseMap(stockInventoryDetailInterface.queryMaterialInventoryQuantity(materialDetails), new com.alibaba.fastjson.TypeReference<Map<String, BigDecimal>>() {
        }, null);
        materialCodeStockMap = materialCodeStockMap == null ? new HashMap<>() : materialCodeStockMap;
        // 获取物料列表
        List<MaterialCodeAndSkuIdSelectDTO> codes = list.stream()
                .map(materielLine -> MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(materielLine.getMaterialCode())
                        .skuId(materielLine.getSkuId()).build())
                .collect(Collectors.toList());
        Map<String, com.yelink.dfs.entity.product.MaterialEntity> codeMaterialMap = materialService.getMaterialEntity(codes);

        for (WorkOrderMaterialListMaterialEntity listMaterialEntity : list) {
            listMaterialEntity.setSubTypeName(BomItemTypeEnum.getNameByCode(listMaterialEntity.getSubType()));
            MaterialEntity materialEntity = codeMaterialMap.get(ColumnUtil.getMaterialSku(listMaterialEntity.getMaterialCode(), listMaterialEntity.getSkuId()));
            listMaterialEntity.setMaterialFields(JacksonUtil.convertObject(materialEntity, com.yelink.dfscommon.entity.dfs.MaterialEntity.class));
            // 查询关联的bom物料行的损耗率、固定损耗
            BomRawMaterialEntity bomRawMaterialEntity = bomRawMaterialMap.get(listMaterialEntity.getBomRawMaterialId());
            listMaterialEntity.setLossRate(Objects.isNull(bomRawMaterialEntity) ? 0.0 : bomRawMaterialEntity.getLossRate());
            listMaterialEntity.setFixedDamage(Objects.isNull(bomRawMaterialEntity) ? 0.0 : bomRawMaterialEntity.getFixedDamage());
            BigDecimal stockQuantity = materialCodeStockMap.getOrDefault(ColumnUtil.getMaterialSku(listMaterialEntity.getMaterialCode(), listMaterialEntity.getSkuId()), BigDecimal.ZERO);
            listMaterialEntity.setStockQuantity(stockQuantity);
            // 如果replaceMaterialId为空，且关联的bom物料行id，则查询判断是否存在替代方案，如果存在则放到replaceMaterialId上
            listMaterialEntity.setReplaceMaterialId(StringUtils.isBlank(listMaterialEntity.getReplaceMaterialId()) ? replaceMaterialMap.get(listMaterialEntity.getBomRawMaterialId()) : listMaterialEntity.getReplaceMaterialId());
            // 计算未领数量、累计计划数量、累计领用数量
            calQuantity(listMaterialEntity);
        }

        // 批量设置物料的下推标识信息
        batchSetPushDownIdentifierInfos(list);

        return list;
    }

    /**
     * 批量设置物料的下推标识信息
     *
     * @param materialEntities 工单用料清单物料实体列表
     */
    private void batchSetPushDownIdentifierInfos(List<WorkOrderMaterialListMaterialEntity> materialEntities) {
        if (CollectionUtils.isEmpty(materialEntities)) {
            return;
        }

        // 收集所有物料ID
        List<String> materialIds = materialEntities.stream()
                .filter(entity -> entity.getId() != null)
                .map(entity -> entity.getId().toString())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(materialIds)) {
            return;
        }

        // 批量查询下推标识信息
        Map<String, List<PushDownIdentifierInfoDTO>> identifierInfoMap = orderPushDownIdentifierService
                .batchGetPushDownIdentifierInfos(OrderNumTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode(), materialIds);

        // 为每个物料设置下推标识信息
        for (WorkOrderMaterialListMaterialEntity materialEntity : materialEntities) {
            if (materialEntity.getId() != null) {
                String materialId = materialEntity.getId().toString();
                List<PushDownIdentifierInfoDTO> identifierInfos = identifierInfoMap.getOrDefault(materialId, new ArrayList<>());
                materialEntity.setPushDownIdentifierInfos(identifierInfos);
            }
        }
    }

    /**
     * 设置单个物料的下推标识信息（已废弃，改为批量查询）
     *
     * @param materialEntity 工单用料清单物料实体
     * @deprecated 请使用 batchSetPushDownIdentifierInfos 进行批量设置
     */
    @Deprecated
    private void setPushDownIdentifierInfoForMaterial(WorkOrderMaterialListMaterialEntity materialEntity) {
        if (materialEntity == null || materialEntity.getId() == null) {
            return;
        }

        List<PushDownIdentifierInfoDTO> identifierInfos = orderPushDownIdentifierService
                .getPushDownIdentifierInfos(OrderNumTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode(),
                                           materialEntity.getId().toString());
        materialEntity.setPushDownIdentifierInfos(identifierInfos);
    }

    /**
     * 调整用料清单物料的位置，呈现标准件与替代件的子父级关系
     */
    @Override
    public List<WorkOrderMaterialListMaterialEntity> sortMaterialEntities(List<WorkOrderMaterialListMaterialEntity> materialEntities) {
        List<WorkOrderMaterialListMaterialEntity> list = new ArrayList<>();
        List<WorkOrderMaterialListMaterialEntity> parentList = materialEntities.stream()
                .filter(o -> Objects.isNull(o.getMainMaterialCode()))
                .collect(Collectors.toList());
        Map<String, List<WorkOrderMaterialListMaterialEntity>> childrenMap = materialEntities.stream()
                .filter(o -> Objects.nonNull(o.getMainMaterialCode()))
                .collect(Collectors.groupingBy(o -> o.getMaterialListCode() + Constants.UNDER_LINE + o.getMainMaterialCode()));
        for (WorkOrderMaterialListMaterialEntity parentEntity : parentList) {
            Map<String, Integer> map = new HashMap<>();
            list.add(parentEntity);
            if (StringUtils.isNotBlank(parentEntity.getSelectedReplaceMaterialId())) {
                // 查询标准件关联替代方案的替代物列表
                ReplaceSchemeSelectDTO selectDTO = ReplaceSchemeSelectDTO.builder()
                        .replaceMaterialIds(parentEntity.getSelectedReplaceMaterialId())
                        .showType(ShowTypeEnum.MATERIAL.getType())
                        .build();
                Page<ReplaceSchemeEntity> schemeEntityPage = replaceSchemeService.getList(selectDTO);
                List<ReplaceSchemeMaterialEntity> schemeMaterialEntities = schemeEntityPage.getRecords().stream().map(ReplaceSchemeEntity::getSchemeMaterialEntity).collect(Collectors.toList());
                map = schemeMaterialEntities.stream().collect(Collectors.toMap(ReplaceSchemeMaterialEntity::getReplaceMaterialCode, ReplaceSchemeMaterialEntity::getId));
            }
            List<WorkOrderMaterialListMaterialEntity> childrenList = childrenMap.get(parentEntity.getMaterialListCode() + Constants.UNDER_LINE + parentEntity.getMaterialCode());
            if (!CollectionUtils.isEmpty(childrenList)) {
                list.addAll(childrenList);
                // 以下数据关联是为了方便前端操作
                for (WorkOrderMaterialListMaterialEntity listMaterialEntity : childrenList) {
                    // 设置父id
                    listMaterialEntity.setParentId(parentEntity.getId());
                    // 设置关联的替代方案物料id
                    listMaterialEntity.setRelatedReplaceMaterialId(map.get(listMaterialEntity.getMaterialCode()));
                }
            }
        }
        return list;
    }

    /**
     * 计算未领数量、累计计划数量、累计领用数量
     */
    @Override
    public void calQuantity(WorkOrderMaterialListMaterialEntity materialEntity) {
        BigDecimal actualQuantity = Objects.nonNull(materialEntity.getActualQuantity()) ? materialEntity.getActualQuantity() : BigDecimal.ZERO;
        materialEntity.setActualQuantity(actualQuantity);
        // 未领数量：计划用量-实际用量
        materialEntity.setUncollectedQuantity(materialEntity.getPlanQuantity().subtract(actualQuantity));
        // 累计计划数量: 标准件计划数量 + 替代料的替代数量之和，替代料不存在此字段
        // 累计领用数量: 标准件实际数量 + （替代料的实际用料*主替用量比）之和，替代料不存在此字段
        if (!materialEntity.getSubType().equals(BomItemTypeEnum.REPLACE_ITEM.getCode())) {
            List<WorkOrderMaterialListMaterialEntity> replaceList = this.lambdaQuery()
                    .eq(WorkOrderMaterialListMaterialEntity::getMaterialListCode, materialEntity.getMaterialListCode())
                    .eq(WorkOrderMaterialListMaterialEntity::getMainMaterialCode, materialEntity.getMaterialCode())
                    .list();
            double planQuantitySum = replaceList.stream().mapToDouble(o -> o.getReplaceQuantity().doubleValue()).sum();
            double actualQuantitySum = replaceList.stream().mapToDouble(o ->
                            o.getActualQuantity().multiply(Objects.isNull(o.getBomNumerator()) ? BigDecimal.ONE : o.getBomNumerator())
                                    .divide(Objects.isNull(o.getBomDenominator()) ? BigDecimal.ONE : o.getBomDenominator()).doubleValue())
                    .sum();

            materialEntity.setTotalPlanQuantity(materialEntity.getPlanQuantity().add(BigDecimal.valueOf(planQuantitySum)));
            materialEntity.setTotalReceiveQuantity(materialEntity.getActualQuantity().add(BigDecimal.valueOf(actualQuantitySum)));
        }
    }
}
