package com.yelink.dfs.migration;

import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.utils.SpringUtil;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierStateEnum;
import com.yelink.dfscommon.entity.dfs.OrderPushDownIdentifierEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flywaydb.core.api.migration.BaseJavaMigration;
import org.flywaydb.core.api.migration.Context;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 数据迁移：从OrderPushDownRecordEntity迁移数据到OrderPushDownIdentifierEntity
 *
 * <AUTHOR>
 */
@Slf4j
public class V3_23_1_101__JavaMigration extends BaseJavaMigration {

    @Override
    public void migrate(Context context) {
        try {
            // 迁移数据
            migrateData();
            log.info("数据迁移完成");
        } catch (Exception e) {
            log.error("数据迁移失败", e);
            throw new RuntimeException("数据迁移失败", e);
        }
    }

    /**
     * 执行数据迁移
     */
    private void migrateData() {
        OrderPushDownRecordService pushDownRecordService = SpringUtil.getBean(OrderPushDownRecordService.class);
        OrderPushDownIdentifierService pushDownIdentifierService = SpringUtil.getBean(OrderPushDownIdentifierService.class);
        // 查询所有下推记录数据
        List<OrderPushDownRecordEntity> recordList = pushDownRecordService.list();
        if (recordList.isEmpty()) {
            log.info("没有需要迁移的下推记录数据");
            return;
        }

        log.info("找到 {} 条下推记录数据", recordList.size());

        // 统计各种单据类型的数量
        Map<String, Long> orderTypeCount = recordList.stream()
            .filter(record -> StringUtils.isNotBlank(record.getSourceOrderType()))
            .collect(Collectors.groupingBy(
                OrderPushDownRecordEntity::getSourceOrderType,
                Collectors.counting()
            ));
        log.info("各单据类型统计：{}", orderTypeCount);

        // 过滤有效数据：只处理生产工单和生产工单用料清单类型的记录
        List<OrderPushDownRecordEntity> filteredRecords = recordList.stream()
            .filter(record -> StringUtils.isNotBlank(record.getSourceOrderType())
                && record.getSourceOrderMaterialId() != null
                && isWorkOrderRelatedType(record.getSourceOrderType()))
            .collect(Collectors.toList());

        log.info("过滤后得到 {} 条生产工单相关记录", filteredRecords.size());

        Map<String, List<OrderPushDownRecordEntity>> groupedRecords = filteredRecords.stream()
            .collect(Collectors.groupingBy(this::buildGroupKey));

        log.info("分组后得到 {} 个唯一的标识组合需要迁移", groupedRecords.size());

        if (groupedRecords.isEmpty()) {
            log.info("没有生产工单相关的记录需要迁移");
            return;
        }

        List<OrderPushDownIdentifierEntity> identifierList = new ArrayList<>();
        int processedCount = 0;
        int skippedCount = 0;

        // 处理每个分组
        for (Map.Entry<String, List<OrderPushDownRecordEntity>> entry : groupedRecords.entrySet()) {
            try {
                List<OrderPushDownRecordEntity> records = entry.getValue();
                OrderPushDownRecordEntity firstRecord = records.get(0);

                // 检查是否已存在相同的标识记录
                OrderPushDownIdentifierEntity existingIdentifier = pushDownIdentifierService.getByOrderTypeAndMaterialIdAndBatch(
                    firstRecord.getSourceOrderType(),
                    String.valueOf(firstRecord.getSourceOrderMaterialId()),
                    firstRecord.getSourceOrderBatch()
                );

                if (existingIdentifier != null) {
                    log.debug("标识记录已存在，跳过：{}", entry.getKey());
                    skippedCount++;
                    continue;
                }

                // 创建新的标识记录
                OrderPushDownIdentifierEntity identifier = createIdentifierFromRecord(firstRecord, records);
                identifierList.add(identifier);
                processedCount++;

                // 分批处理，避免内存溢出
                if (identifierList.size() >= 1000) {
                    pushDownIdentifierService.batchAddIdentifier(identifierList);
                    log.info("批量插入 {} 条标识记录", identifierList.size());
                    identifierList.clear();
                }

            } catch (Exception e) {
                log.error("处理分组 {} 时发生错误", entry.getKey(), e);
                // 继续处理其他记录，不中断整个迁移过程
            }
        }

        // 处理剩余的记录
        if (!identifierList.isEmpty()) {
            pushDownIdentifierService.batchAddIdentifier(identifierList);
            log.info("最后批量插入 {} 条标识记录", identifierList.size());
        }

        log.info("数据迁移完成：处理 {} 条，跳过 {} 条", processedCount, skippedCount);
    }

    /**
     * 判断是否为生产工单相关类型
     */
    private boolean isWorkOrderRelatedType(String sourceOrderType) {
        return "workOrder".equals(sourceOrderType) || "workOrderMaterialList".equals(sourceOrderType);
    }

    /**
     * 构建分组键
     */
    private String buildGroupKey(OrderPushDownRecordEntity record) {
        String batchNumber = StringUtils.isBlank(record.getSourceOrderBatch()) ? "NULL" : record.getSourceOrderBatch();
        return record.getSourceOrderType() + "_" + record.getSourceOrderMaterialId() + "_" + batchNumber;
    }

    /**
     * 从下推记录创建标识记录
     */
    private OrderPushDownIdentifierEntity createIdentifierFromRecord(OrderPushDownRecordEntity firstRecord,
                                                                    List<OrderPushDownRecordEntity> allRecords) {
        OrderPushDownIdentifierEntity identifier = new OrderPushDownIdentifierEntity();

        // 设置基本字段
        identifier.setOrderType(firstRecord.getSourceOrderType());
        identifier.setOrderMaterialId(String.valueOf(firstRecord.getSourceOrderMaterialId()));
        identifier.setBatchNumber(firstRecord.getSourceOrderBatch());
        identifier.setTargetOrderType(firstRecord.getTargetOrderType());

        // 计算下推状态
        String state = calculatePushDownState(allRecords);
        identifier.setState(state);

        return identifier;
    }

    /**
     * 根据下推记录计算下推状态
     */
    private String calculatePushDownState(List<OrderPushDownRecordEntity> records) {
        if (records == null || records.isEmpty()) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        }

        // 统计有目标单据的记录数量
        long successfulRecords = records.stream()
            .filter(record -> StringUtils.isNotBlank(record.getTargetOrderNumber()))
            .filter(record -> record.getIsAbnormal() == null || !record.getIsAbnormal())
            .count();

        // 如果没有成功的下推记录，认为是未下推
        if (successfulRecords == 0) {
            return PushDownIdentifierStateEnum.NO_PUSH_DOWN.getCode();
        }

        // 如果所有记录都成功下推，认为是已下推
        if (successfulRecords == records.size()) {
            return PushDownIdentifierStateEnum.ALL_PUSH_DOWN.getCode();
        }

        // 否则认为是部分下推
        return PushDownIdentifierStateEnum.PART_PUSH_DOWN.getCode();
    }
}

